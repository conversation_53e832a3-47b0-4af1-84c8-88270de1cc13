import { Button } from "@/components/ui/Button";
import { Dialog } from "@/components/ui/Dialog";
import { BLOCK_TYPE, type Block, type Note } from "@/stores/useFolderStore";
import { useState } from "react";
import { TextEditor } from "@/components/features/editor/TextEditor";

export const UpsertBlockDialog = ({
  open,
  onOpenChange,
  note,
  block,
}: {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  note: Note;
  block?: Block;
}) => {
  const [blockType, setBlockType] = useState<BLOCK_TYPE | undefined>(block?.block_type);

  const dialogTitle = block?.id ? "Edit" : "Create";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <Dialog.Content className="sm:max-w-[625px]">
        <Dialog.Header>
          <Dialog.Title>
            {dialogTitle} block for {note.title}
          </Dialog.Title>
        </Dialog.Header>

        <Dialog.Description className="text-sm text-muted-foreground mb-4">
          Blocks are the building blocks of your notes. You can add text, statblocks, and more.
        </Dialog.Description>

        {!blockType ? (
          <div className="w-full flex items-center justify-center">
            <div className="flex flex-col gap-2 max-w-sm w-full">
              <Button variant="outline" onClick={() => setBlockType(BLOCK_TYPE.TEXT)}>
                Text
              </Button>
              <Button variant="outline" onClick={() => setBlockType(BLOCK_TYPE.STATBLOCK)}>
                Statblock
              </Button>
            </div>
          </div>
        ) : null}

        {blockType === BLOCK_TYPE.TEXT ? <TextEditor /> : null}
      </Dialog.Content>
    </Dialog>
  );
};
